import { NextRequest, NextResponse } from "next/server";
import { ApiResponse } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import { sendContactConfirmationEmail, sendNewContactMessageNotification } from "@/lib/email-service";
import prisma from "@/lib/prisma";


// POST /api/contact - Submit contact form
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, subject, message } = body;

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { success: false, error: "All fields are required" },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: "Please enter a valid email address" },
        { status: 400 }
      );
    }

    // Validate message length
    if (message.length < 10) {
      return NextResponse.json(
        { success: false, error: "Message must be at least 10 characters long" },
        { status: 400 }
      );
    }

    // Get current user if authenticated
    const user = await getCurrentUser();

    // Create contact message
    const contactMessage = await prisma.contactMessage.create({
      data: {
        name: name.trim(),
        email: email.trim().toLowerCase(),
        subject: subject.trim(),
        message: message.trim(),
        userId: user?.id || null,
        status: "UNREAD",
      },
    });

    // Send confirmation email to customer
    try {
      await sendContactConfirmationEmail(contactMessage);
    } catch (error) {
      console.error("Failed to send confirmation email:", error);
      // Don't fail the request if email fails
    }

    // Send notification email to admin
    try {
      await sendNewContactMessageNotification(contactMessage);
    } catch (error) {
      console.error("Failed to send admin notification email:", error);
      // Don't fail the request if email fails
    }

    const response: ApiResponse<typeof contactMessage> = {
      success: true,
      data: contactMessage,
      message: "Message sent successfully",
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error submitting contact form:", error);
    return NextResponse.json(
      { success: false, error: "Failed to send message" },
      { status: 500 }
    );
  }
}

// GET /api/contact - Get contact messages (admin only)
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user || user.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const status = searchParams.get("status");
    const search = searchParams.get("search");

    const where: any = {};
    
    if (status && status !== "all") {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
        { subject: { contains: search, mode: "insensitive" } },
        { message: { contains: search, mode: "insensitive" } },
      ];
    }

    const skip = (page - 1) * limit;
    const total = await prisma.contactMessage.count({ where });

    const messages = await prisma.contactMessage.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });

    const response: ApiResponse = {
      success: true,
      data: {
        data: messages,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching contact messages:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch messages" },
      { status: 500 }
    );
  }
}
